package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.GrauInstrucaoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroGrauInstrucaoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.GrauInstrucaoService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaGrauInstrucao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaListGrauInstrucaoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.grauinstrucao.ExemploRespostaVazia;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grau-instrucao")

public class GrauInstrucaoController {
    private final GrauInstrucaoService grauInstrucaoService;

    public GrauInstrucaoController(GrauInstrucaoService grauInstrucaoService) {
        this.grauInstrucaoService = grauInstrucaoService;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO) {
        try {
            FiltroGrauInstrucaoJSON filtroGrauInstrucaoJSON = new FiltroGrauInstrucaoJSON(filtros);
            return ResponseEntityFactory.ok(grauInstrucaoService.findAll(filtroGrauInstrucaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> grauInstrucao(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(grauInstrucaoService.grauInstrucao(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirGrauInstrucao(@RequestBody GrauInstrucaoDTO grauInstrucao) {
        try {
            return ResponseEntityFactory.ok(grauInstrucaoService.saveOrUpdate(grauInstrucao));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarGrauInstrucao(@PathVariable Integer id ) {
        try {
            grauInstrucaoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
